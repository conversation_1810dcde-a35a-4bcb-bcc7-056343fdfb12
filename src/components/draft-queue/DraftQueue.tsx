'use client'

import { useState } from 'react'
import { useDraftQueue } from '@/hooks/useDraftQueue'
import { useNavigation } from '@/hooks/useNavigation'
import { useI18n } from '@/hooks/useI18n'
import { formatDuration } from '@/lib/utils/format'
import { formatTime } from '@/lib/utils/time'
import { DraftVideoItem, VideoTimeframe } from '@/lib/types/video'

// Component for editing individual draft item properties
function DraftItemEditor({ item, onUpdate, onRemove }: {
  item: DraftVideoItem
  onUpdate: (updates: Partial<Pick<DraftVideoItem, 'timeframes' | 'loopSettings'>>) => void
  onRemove: () => void
}) {
  const { activeView, enterTimeframeEditing } = useNavigation()

  const handleEditVideo = () => {
    enterTimeframeEditing({
      itemId: item.draftId,
      mode: 'edit',
      returnView: activeView as any
    })
  }

  return (
    <div className="flex items-start gap-3 p-3 bg-dark-700/30 rounded-xl border border-dark-600/50 hover:border-dark-500/70 transition-all">
      {/* Thumbnail with loop count badge */}
      <div className="relative flex-shrink-0">
        <img
          src={item.thumbnail}
          alt={item.title}
          className="w-16 h-12 rounded-lg object-cover"
          loading="lazy"
        />
        <div className="absolute -top-1 -left-1 w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
          {item.loopSettings.videoLoopCount}
        </div>
      </div>

      {/* Video Info */}
      <div className="flex-1 min-w-0">
        <div className="text-white font-medium text-sm line-clamp-2 mb-1">
          {item.title}
        </div>
        <div className="text-dark-300 text-xs mb-2">
          {formatDuration(item.duration)} • {item.channel || 'YouTube'}
        </div>
        
        {/* Enhanced Compact Video Loop Settings Display */}
        <div className="space-y-1.5">
          {/* Video Settings Badge */}
          <div className="flex items-center gap-1.5 text-xs">
            <div className="flex items-center gap-1 text-purple-300 bg-purple-500/10 px-2 py-1 rounded border border-purple-500/20">
              <div className="w-3 h-3 bg-purple-500 rounded-sm flex items-center justify-center">
                <svg width="8" height="8" viewBox="0 0 24 24" fill="white">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <span className="font-medium">{t('timeframeEditor.videoLoopCount')}: {item.loopSettings.videoLoopCount}x</span>
              <span className="text-purple-400">•</span>
              <span>{item.loopSettings.loopMode === 'timeframes-only' ? t('timeframeEditor.timeframesOnly') : t('timeframeEditor.fullVideoPlusTimeframes')}</span>
            </div>
          </div>
          
          {/* Timeframes Badge */}
          {item.timeframes.length > 0 ? (
            <div className="flex items-center gap-1.5 text-xs">
              <div className="flex items-center gap-1 text-green-300 bg-green-500/10 px-2 py-1 rounded border border-green-500/20">
                <div className="w-3 h-3 bg-green-500 rounded-sm flex items-center justify-center">
                  <svg width="8" height="8" viewBox="0 0 24 24" fill="white">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <span className="font-medium">{item.timeframes.length} {item.timeframes.length > 1 ? t('labels.timeframes') : t('labels.timeframe')}</span>
                <span className="text-green-400">•</span>
                <span className="font-mono">
                  {item.timeframes.map((tf, index) => 
                    `${formatTime(tf.startTime)}-${formatTime(tf.endTime)}(${tf.loopCount}x)`
                  ).join(', ')}
                </span>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-1.5 text-xs">
              <div className="flex items-center gap-1 text-dark-400 bg-dark-600/20 px-2 py-1 rounded border border-dark-600/30">
                <div className="w-3 h-3 bg-dark-500 rounded-sm flex items-center justify-center">
                  <svg width="8" height="8" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                  </svg>
                </div>
                <span>{t('labels.noTimeframesConfigured')}</span>
                <span className="text-dark-500">•</span>
                <span className="italic">{t('labels.clickEditToAdd')}</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center space-x-2">
        <button
          onClick={handleEditVideo}
          className="p-2 text-dark-300 hover:text-primary-400 hover:bg-primary-400/10 rounded-lg transition-colors"
          title={t('tooltips.editVideoSettings')}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
          </svg>
        </button>
        <button
          onClick={onRemove}
          className="p-2 text-dark-300 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
          title={t('tooltips.removeFromDraft')}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>
  )
}

export function DraftQueue() {
  const { draftItems, draftCount, removeFromDraft, updateDraftItem, clearDraft } = useDraftQueue()
  const { t } = useI18n()

  if (draftCount === 0) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold text-white">
            Draft Queue <span className="text-primary-400">({draftCount})</span>
          </h4>
        </div>
        <div className="text-center py-8">
          <div className="text-4xl mb-4">🎵</div>
          <p className="text-dark-300 mb-2">{t('messages.noVideosInDraft')}</p>
          <p className="text-dark-400 text-sm">{t('messages.searchAndAddVideos')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="glassmorphism rounded-2xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-semibold text-white">
          Draft Queue <span className="text-primary-400">({draftCount})</span>
        </h4>
        <button
          onClick={clearDraft}
          className="btn-secondary text-sm px-3 py-2"
          title={t('tooltips.clearDraftQueue')}
        >
          {t('buttons.clearAll')}
        </button>
      </div>
      <div className="space-y-3">
        {draftItems.map((item, index) => (
          <DraftItemEditor
            key={item.draftId}
            item={item}
            onUpdate={(updates) => updateDraftItem(item.draftId, updates)}
            onRemove={() => removeFromDraft(item.draftId)}
          />
        ))}
      </div>
    </div>
  )
}
