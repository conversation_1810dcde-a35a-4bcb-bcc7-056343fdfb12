'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Queue } from '@/lib/types/queue'
import { formatRelativeTime, formatDuration } from '@/lib/utils/format'
import { firebaseService } from '@/lib/services/firebase'
import { useAuth } from '@/hooks/useAuth'
import { useDraftQueue } from '@/hooks/useDraftQueue'
import { useNavigation } from '@/hooks/useNavigation'
import { useI18n } from '@/hooks/useI18n'

interface PersonalQueueCardProps {
  queue: Queue
  onLoad: (queue: Queue) => void
  onDelete: (queueId: string) => void
  onPrivacyUpdate?: () => void
  onUpdate?: (updatedQueue: Queue) => void
  isSelectionMode?: boolean
  isSelected?: boolean
  onSelectionChange?: (queue: Queue, selected: boolean) => void
}

export function PersonalQueueCard({
  queue,
  onLoad,
  onDelete,
  onPrivacyUpdate,
  onUpdate,
  isSelectionMode = false,
  isSelected = false,
  onSelectionChange
}: PersonalQueueCardProps) {
  const { user } = useAuth()
  const { enterEditMode } = useDraftQueue()
  const { setActiveView } = useNavigation()
  const [isUpdatingPrivacy, setIsUpdatingPrivacy] = useState(false)
  const [isPublic, setIsPublic] = useState(queue.isPublic || false)
  const [linkCopied, setLinkCopied] = useState(false)
  const [isDuplicating, setIsDuplicating] = useState(false)
  const { t } = useI18n()

  // Sync local isPublic state with queue prop changes
  useEffect(() => {
    setIsPublic(queue.isPublic || false)
  }, [queue.isPublic])

  const handlePrivacyToggle = async () => {
    if (!user || isUpdatingPrivacy) return

    setIsUpdatingPrivacy(true)
    const newPrivacyStatus = !isPublic

    try {
      const success = await firebaseService.updateQueue(
        queue.id,
        { isPublic: newPrivacyStatus },
        user.uid
      )

      if (success) {
        setIsPublic(newPrivacyStatus)
        console.log(`✅ Queue ${newPrivacyStatus ? 'made public' : 'made private'}`)

        // Update the queue in local state instead of refreshing the entire list
        const updatedQueue = {
          ...queue,
          isPublic: newPrivacyStatus,
          lastModified: Date.now()
        }
        onUpdate?.(updatedQueue)
      } else {
        console.error('Failed to update queue privacy')
      }
    } catch (error) {
      console.error('Error updating queue privacy:', error)
    } finally {
      setIsUpdatingPrivacy(false)
    }
  }

  const handleLoad = () => {
    onLoad(queue)
  }

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this queue?')) {
      onDelete(queue.id)
    }
  }

  const handleEdit = () => {
    // Enter edit mode with the queue data
    enterEditMode(queue)
    // Navigate to search view where the draft queue and creation form will be visible
    setActiveView('search')
  }

  const handleDuplicate = async () => {
    if (!user || isDuplicating) return

    setIsDuplicating(true)
    try {
      const duplicatedQueueId = await firebaseService.duplicateQueue(
        queue.id,
        user.uid,
        `${queue.metadata.title} (Copy)`
      )

      if (duplicatedQueueId) {
        console.log('✅ Queue duplicated successfully')
        onPrivacyUpdate?.() // Still need to refresh for duplication since it creates a new queue
      } else {
        console.error('Failed to duplicate queue')
      }
    } catch (error) {
      console.error('Error duplicating queue:', error)
    } finally {
      setIsDuplicating(false)
    }
  }

  const handleSelectionChange = () => {
    if (isSelectionMode && onSelectionChange) {
      onSelectionChange(queue, !isSelected)
    }
  }

  const thumbnail = queue.metadata.firstVideoThumbnail || queue.queueData.items[0]?.thumbnail

  return (
    <div className={`glassmorphism rounded-lg overflow-hidden hover:bg-white/5 transition-all duration-200 group hover:scale-[1.02] hover:shadow-xl ${isUpdatingPrivacy ? 'opacity-75 pointer-events-none' : ''} ${isSelected ? 'ring-2 ring-primary-500' : ''}`}>
      {/* Selection Checkbox */}
      {isSelectionMode && (
        <div className="absolute top-3 left-3 z-10">
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={handleSelectionChange}
              className="w-5 h-5 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500 focus:ring-2"
            />
          </label>
        </div>
      )}
      {/* Queue Thumbnail */}
      <div className="aspect-video bg-dark-800 relative">
        {thumbnail ? (
          <Image
            src={thumbnail}
            alt={queue.metadata.title || 'Queue thumbnail'}
            fill
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-600/20 to-purple-600/20">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </div>
        )}

        {/* Play Overlay */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
          <button
            onClick={handleLoad}
            className="bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full transition-colors duration-200"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        </div>

        {/* Video Count Badge */}
        <div className="absolute top-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
          {queue.metadata.videoCount || queue.queueData.items.length} {t('labels.videos')}
        </div>

        {/* Privacy Badge */}
        <div className="absolute top-2 left-2">
          <span className={`text-xs px-2 py-1 rounded ${
            isPublic 
              ? 'bg-green-600/80 text-green-100' 
              : 'bg-gray-600/80 text-gray-100'
          }`}>
            {isPublic ? 'Public' : 'Private'}
          </span>
        </div>
      </div>

      {/* Queue Info */}
      <div className="p-4">
        <h4 className="font-medium text-white truncate mb-1">
          {queue.metadata.title || t('messages.untitledQueue')}
        </h4>
        <p className="text-sm text-dark-300 mb-2">
          {formatDuration(queue.metadata.totalDuration || 0)} • {formatRelativeTime(queue.lastModified)}
        </p>
        {queue.metadata.description && (
          <p className="text-xs text-dark-400 line-clamp-2 mb-3">
            {queue.metadata.description}
          </p>
        )}

        {/* Privacy Toggle */}
        <div className="flex items-center justify-between mb-3 p-2 bg-dark-800/50 rounded-lg">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-dark-300 font-medium">
              {isPublic ? t('labels.makePrivate') : t('labels.makePublic')}
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={isPublic}
                onChange={handlePrivacyToggle}
                disabled={isUpdatingPrivacy}
                className="sr-only peer"
              />
              <div className="w-10 h-6 bg-dark-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-500/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600 peer-disabled:opacity-50"></div>
            </label>
          </div>
          {isUpdatingPrivacy && (
            <div className="loading-spinner w-4 h-4"></div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="text-xs text-dark-400 flex items-center">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" className="mr-1">
                <path d="M8 5v14l11-7z"/>
              </svg>
              {queue.metadata.viewCount || 0} {t('labels.plays')}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleEdit}
              className="p-2 text-dark-400 hover:text-blue-400 hover:bg-blue-400/10 rounded-lg transition-all duration-200"
              title={t('buttons.editQueue')}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
              </svg>
            </button>
            <button
              onClick={handleDuplicate}
              disabled={isDuplicating}
              className="p-2 text-dark-400 hover:text-green-400 hover:bg-green-400/10 rounded-lg transition-all duration-200 disabled:opacity-50"
              title={t('buttons.duplicateQueue')}
            >
              {isDuplicating ? (
                <div className="loading-spinner w-4 h-4"></div>
              ) : (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                </svg>
              )}
            </button>
            <button
              onClick={handleDelete}
              className="p-2 text-dark-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-all duration-200"
              title={t('buttons.deleteQueue')}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Share Link (only show if public) */}
        {isPublic && (
          <div className="mt-3 pt-3 border-t border-white/10">
            <div className="flex items-center justify-between">
              <span className="text-xs text-dark-400 flex items-center">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" className="mr-1">
                  <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
                </svg>
                Share Link
              </span>
              <button
                onClick={async () => {
                  try {
                    const shareUrl = `${window.location.origin}?q=${queue.id}`
                    await navigator.clipboard.writeText(shareUrl)
                    setLinkCopied(true)
                    setTimeout(() => setLinkCopied(false), 2000)
                    console.log('Share link copied to clipboard')
                  } catch (error) {
                    console.error('Failed to copy link:', error)
                  }
                }}
                className={`text-xs transition-colors duration-200 flex items-center px-2 py-1 rounded ${
                  linkCopied
                    ? 'text-green-400 bg-green-400/10'
                    : 'text-primary-400 hover:text-primary-300 hover:bg-primary-400/10'
                }`}
              >
                {linkCopied ? (
                  <>
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" className="mr-1">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                    Copied!
                  </>
                ) : (
                  <>
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" className="mr-1">
                      <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                    Copy
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
