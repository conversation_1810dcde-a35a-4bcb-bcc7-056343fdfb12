'use client'

import { useState, useEffect } from 'react'
import { SearchInput } from './SearchInput'
import { SearchResults } from './SearchResults'
import { ManualLinkInput } from './ManualLinkInput'
import { youtubeService } from '@/lib/services/youtube'
import { VideoSearchResult, PaginatedSearchResult } from '@/lib/types/video'
import { useAuth } from '@/hooks/useAuth'
import { useI18n } from '@/hooks/useI18n'

import { useDraftQueue } from '@/hooks/useDraftQueue'
import { DraftQueue } from '@/components/draft-queue/DraftQueue'
import { QueueCreationForm } from '@/components/draft-queue/QueueCreationForm'

export function SearchView() {
  const { user, isAuthenticated } = useAuth()
  const { t } = useI18n()
  const {
    draftItems,
    draftCount,
    isCreationMode,
    isEditMode,
    enterCreationMode,
    exitCreationMode
  } = useDraftQueue()

  // Mode state
  const [inputMode, setInputMode] = useState<'search' | 'manual'>('search')

  // Search state
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [searchResults, setSearchResults] = useState<VideoSearchResult[]>([])
  const [error, setError] = useState<string | null>(null)
  const [paginationData, setPaginationData] = useState<{
    nextPageToken?: string
    prevPageToken?: string
    totalResults: number
    currentPage: number
    hasNextPage: boolean
    hasPrevPage: boolean
  } | null>(null)

  // Automatically enter creation mode when SearchView becomes active (unless already in edit mode)
  useEffect(() => {
    if (!isCreationMode && !isEditMode) {
      enterCreationMode()
    }
  }, [isCreationMode, isEditMode, enterCreationMode])

  const handleSearch = async (query: string, pageToken?: string) => {
    if (!query.trim()) {
      setSearchResults([])
      setError(null)
      setPaginationData(null)
      return
    }

    if (pageToken) {
      setIsLoadingMore(true)
    } else {
      setIsSearching(true)
      setSearchQuery(query)
      setSearchResults([]) // Clear results for new search
      setPaginationData(null)
    }
    setError(null)

    try {
      console.log('🔍 Searching YouTube for:', query, pageToken ? `(page token: ${pageToken})` : '')
      const result = await youtubeService.searchVideosPaginated(query, 20, pageToken)

      if (pageToken) {
        // Append results for pagination, but deduplicate by ID
        setSearchResults(prev => {
          const existingIds = new Set(prev.map(item => item.id))
          const newResults = result.results.filter(item => !existingIds.has(item.id))
          return [...prev, ...newResults]
        })
      } else {
        // Replace results for new search
        setSearchResults(result.results)
      }

      setPaginationData({
        nextPageToken: result.nextPageToken,
        prevPageToken: result.prevPageToken,
        totalResults: result.totalResults,
        currentPage: result.currentPage,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage
      })

      console.log('✅ Found', result.results.length, 'videos', pageToken ? '(loaded more)' : '')
    } catch (error: any) {
      console.error('❌ Search error:', error)

      // Check for 403 error specifically
      if (error.message && error.message.includes('403')) {
        setError('quota_exceeded')
      } else {
        setError(error.message || 'Failed to search videos')
      }

      if (!pageToken) {
        setSearchResults([])
        setPaginationData(null)
      }
    } finally {
      if (pageToken) {
        setIsLoadingMore(false)
      } else {
        setIsSearching(false)
      }
    }
  }

  const handleLoadMore = () => {
    if (paginationData?.hasNextPage && paginationData.nextPageToken && searchQuery) {
      handleSearch(searchQuery, paginationData.nextPageToken)
    }
  }

  return (
    <div className="space-y-6">
      {/* Queue Creation Mode Header */}
      {isCreationMode && (
        <div className="glassmorphism rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                +
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">{t('queueCreation.creatingNewQueue')}</h3>
                <p className="text-dark-300">{t('queueCreation.addVideosToCreate')}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search Section (show when in creation or edit mode) */}
      {(isCreationMode || isEditMode) && (
        <>
          {/* Search Header */}
          <div className="glassmorphism rounded-2xl p-6">
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-white mb-2">
                {isEditMode ? t('search.editTitle') : t('search.title')}
              </h1>
              <p className="text-dark-300">
                {isEditMode ? t('search.editDescription') : t('search.description')}
              </p>
            </div>

            {/* Mode Toggle */}
            <div className="flex items-center justify-center mb-6">
              <div className="bg-dark-800 rounded-lg p-1 flex">
                <button
                  onClick={() => setInputMode('search')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    inputMode === 'search'
                      ? 'bg-primary-500 text-white'
                      : 'text-dark-300 hover:text-white'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                    <span>{t('buttons.search')}</span>
                  </div>
                </button>
                <button
                  onClick={() => setInputMode('manual')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    inputMode === 'manual'
                      ? 'bg-primary-500 text-white'
                      : 'text-dark-300 hover:text-white'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z"/>
                    </svg>
                    <span>{t('buttons.manualLink')}</span>
                  </div>
                </button>
              </div>
            </div>

            {/* Input based on mode */}
            {inputMode === 'search' ? (
              <SearchInput
                onSearch={handleSearch}
                isLoading={isSearching}
                placeholder="Search for videos, artists, or songs..."
              />
            ) : (
              <ManualLinkInput placeholder="Paste YouTube link here (e.g., https://youtube.com/watch?v=...)" />
            )}
          </div>

          {/* Error Message */}
          {error && (
            <div className="glassmorphism rounded-2xl p-6">
              {error === 'quota_exceeded' ? (
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-red-500/20 rounded-full flex items-center justify-center">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-red-400">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">{t('errors.youtubeApiLimit')}</h3>
                  <p className="text-dark-300 mb-4">
                    {t('errors.quotaExceeded')}
                  </p>
                  <button
                    onClick={() => {
                      setInputMode('manual')
                      setError(null)
                    }}
                    className="btn-primary px-4 py-2"
                  >
                    {t('buttons.switchToManualInput')}
                  </button>
                </div>
              ) : (
                <div className="flex items-center space-x-3 text-red-400">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                  </svg>
                  <span>{error}</span>
                </div>
              )}
            </div>
          )}

          {/* Search Results or Empty State */}
          {inputMode === 'search' && (
            <>
              {searchResults.length > 0 || isSearching || isLoadingMore ? (
                <SearchResults
                  results={searchResults}
                  isLoading={isSearching}
                  isLoadingMore={isLoadingMore}
                  query={searchQuery}
                  error={error}
                  paginationData={paginationData}
                  onLoadMore={handleLoadMore}
                />
              ) : searchQuery && !error ? (
                <div className="glassmorphism rounded-2xl p-6">
                  <div className="text-center py-8">
                    <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-white mb-2">{t('search.noResultsFound')}</h3>
                    <p className="text-dark-300 mb-4">
                      {t('search.noVideosFoundFor', { query: searchQuery })}
                    </p>
                    <button
                      onClick={() => {
                        setInputMode('manual')
                        setError(null)
                      }}
                      className="btn-secondary px-4 py-2"
                    >
                      {t('buttons.tryManualInput')}
                    </button>
                  </div>
                </div>
              ) : null}
            </>
          )}
        </>
      )}

      {/* Draft Queue Display (show when in creation or edit mode) - MOVED TO BOTTOM */}
      {(isCreationMode || isEditMode) && <DraftQueue />}

      {/* Queue Creation/Edit Form (show when in creation or edit mode) - MOVED TO BOTTOM */}
      {(isCreationMode || isEditMode) && (
        <QueueCreationForm />
      )}
    </div>
  )
}
