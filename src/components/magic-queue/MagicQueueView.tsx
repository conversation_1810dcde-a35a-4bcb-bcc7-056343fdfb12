'use client'

import { useState, useEffect } from 'react'
import { MagicPromptInput } from './MagicPromptInput'
import { MagicQueueResults } from './MagicQueueResults'
import { aiService, MagicQueueResponse } from '@/lib/services/ai'
import { useDraftQueue } from '@/hooks/useDraftQueue'
import { useI18n } from '@/hooks/useI18n'
import { DraftQueue } from '@/components/draft-queue/DraftQueue'
import { QueueCreationForm } from '@/components/draft-queue/QueueCreationForm'
import { useAuth } from '@/hooks/useAuth'

export function MagicQueueView() {
  const { user, isAuthenticated } = useAuth()
  const {
    draftItems,
    draftCount,
    isCreationMode,
    isEditMode,
    enterCreationMode,
    exitCreationMode
  } = useDraftQueue()

  // Magic queue state
  const [isGenerating, setIsGenerating] = useState(false)
  const [magicResults, setMagicResults] = useState<MagicQueueResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [progressStep, setProgressStep] = useState<string>('')
  const [progressCurrent, setProgressCurrent] = useState<number>(0)
  const [progressTotal, setProgressTotal] = useState<number>(0)
  const { t } = useI18n()

  // Automatically enter creation mode when MagicQueueView becomes active (unless already in edit mode)
  useEffect(() => {
    if (!isCreationMode && !isEditMode) {
      enterCreationMode()
    }
  }, [isCreationMode, isEditMode, enterCreationMode])



  const handleGenerateMagicQueue = async (prompt: string, count: number, maxDuration?: number) => {
    if (!prompt.trim()) return

    setIsGenerating(true)
    setError(null)
    setMagicResults(null)
    setProgressStep('Starting...')
    setProgressCurrent(0)
    setProgressTotal(3)

    try {
      console.log('🪄 Generating magic queue for prompt:', prompt)
      console.log('📊 Parameters:', { count, maxDuration })

      const response = await aiService.generateVideoRecommendations({
        prompt: prompt.trim(),
        count,
        maxDuration,
        onProgress: (step: string, current: number, total: number) => {
          setProgressStep(step)
          setProgressCurrent(current)
          setProgressTotal(total)
        }
      })

      setMagicResults(response)
      console.log('✅ Magic queue generated successfully:', response)
    } catch (error) {
      console.error('❌ Failed to generate magic queue:', error)
      setError(error instanceof Error ? error.message : 'Failed to generate magic queue. Please try again.')
    } finally {
      setIsGenerating(false)
      setProgressStep('')
      setProgressCurrent(0)
      setProgressTotal(0)
    }
  }



  return (
    <div className="space-y-6">
      {/* Queue Creation Mode Header */}
      {isCreationMode && (
        <div className="glassmorphism rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M7.5 5.6L10 7L8.6 4.5L10 2L7.5 3.4L5 2L6.4 4.5L5 7L7.5 5.6ZM19.5 15.4L22 14L20.6 16.5L22 19L19.5 17.6L17 19L18.4 16.5L17 14L19.5 15.4ZM22 2L20.6 4.5L22 7L19.5 5.6L17 7L18.4 4.5L17 2L19.5 3.4L22 2Z"/>
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">{t('queueCreation.creatingMagicQueue')}</h3>
                <p className="text-dark-300">{t('queueCreation.letAICurate')}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Magic Prompt Input */}
      <div className="glassmorphism rounded-2xl p-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            {isEditMode ? t('magicQueue.editQueueRecommendations') : t('magicQueue.generator')}
          </h1>
          <p className="text-dark-300">
            {isEditMode
              ? t('magicQueue.editDescription')
              : t('magicQueue.description')
            }
          </p>
        </div>

        <MagicPromptInput
          onSubmit={handleGenerateMagicQueue}
          isLoading={isGenerating}
          placeholder="Describe what kind of videos you'd like to watch..."
        />
      </div>

      {/* Error Display */}
      {error && (
        <div className="glassmorphism rounded-2xl p-6 border border-red-500/20">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 text-red-400 flex-shrink-0">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div>
              <h3 className="text-red-400 font-semibold mb-1">{t('common.error')}</h3>
              <p className="text-dark-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Magic Queue Results */}
      {(magicResults || isGenerating) && (
        <MagicQueueResults
          videos={magicResults?.videos || []}
          explanation={magicResults?.explanation}
          searchQueries={magicResults?.searchQueries}
          isLoading={isGenerating}
          progressStep={progressStep}
          progressCurrent={progressCurrent}
          progressTotal={progressTotal}
        />
      )}

      {/* Draft Queue Display (show when in creation or edit mode) */}
      {(isCreationMode || isEditMode) && <DraftQueue />}

      {/* Queue Creation/Edit Form (show when in creation or edit mode) */}
      {(isCreationMode || isEditMode) && (
        <QueueCreationForm />
      )}
    </div>
  )
}
